import React, {useEffect, useState} from "react";
import {GenerationRequestData} from "@/lib/api";
import {getStatusString} from "@/lib/utils";
import {Button} from "@/components/ui/button";
import {Maximize2, X} from "lucide-react";
import {Dialog, DialogContent, DialogTitle} from "@/components/ui/dialog";
import {VisuallyHidden} from "@radix-ui/react-visually-hidden";
import {usePBContext} from "@/context/PocketbaseContext";

const ActiveGeneration = ({activeGeneration}: {activeGeneration?: GenerationRequestData}) => {
  const { pb } = usePBContext();
  // Full-screen modal state
  const [isFullScreenOpen, setIsFullScreenOpen] = useState<boolean>(false);

  return (
    <>
      {activeGeneration && (
        <div className="mt-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Текущие генерации</h3>
          <div key={activeGeneration.id} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            {activeGeneration.type === 'video' ? 'Генерация видео' : 'Генерация изображения'}
                          </span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  activeGeneration.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    activeGeneration.status === 'completed' ? 'bg-green-100 text-green-800' :
                      activeGeneration.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                }`}>
                            {getStatusString(activeGeneration.status)}
                          </span>
              </div>
              {activeGeneration.status === 'pending' && (
                <div className="w-4 h-4 border-2 border-[var(--primary-blue)] border-t-transparent rounded-full animate-spin"/>
              )}
            </div>

            <p className="text-sm text-gray-600 mb-3 truncate">{activeGeneration.jsondata?.original_prompt || activeGeneration.jsondata?.prompt || 'Нет prompt-а'}</p>

            {activeGeneration.output_files && activeGeneration.output_files.length > 0 && (
              <div className="space-y-3">
                {activeGeneration.type === 'video' ? (
                  <div className="aspect-video bg-black rounded-lg flex items-center justify-center text-white relative group">
                    <video
                      src={pb.files.getURL(activeGeneration.expand?.output_files[0], activeGeneration.expand?.output_files[0].file)}
                      controls
                      className="w-full h-full rounded-lg"
                    >
                      Ваш браузер не поддерживает видео тег.
                    </video>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-8 w-8 p-0"
                      onClick={() => setIsFullScreenOpen(true)}
                      title="Развернуть на весь экран"
                    >
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {activeGeneration.expand?.output_files.map((outputFile: any, index: number) => (
                      <div key={outputFile.id} className="relative group">
                        <img
                          src={pb.files.getURL(outputFile, outputFile.file)}
                          alt={`Созданное изображение ${index + 1}`}
                          className="w-full h-auto rounded-lg border border-gray-200"
                        />
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-8 w-8 p-0"
                          onClick={() => setIsFullScreenOpen(true)}
                          title="Развернуть на весь экран"
                        >
                          <Maximize2 className="h-4 w-4" />
                        </Button>

                        <Button
                          key={outputFile.id}
                          size="sm"
                          variant="ghost"
                          onClick={()=> {
                            const url = pb.files.getURL(outputFile, outputFile.file);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `generation-${activeGeneration.id}`;
                            link.click();
                          }}
                          className="text-sm text-[var(--primary-blue)] hover:text-[var(--primary-blue-hover)] underline"
                        >
                          Скачать {activeGeneration.type === 'video' ? 'видео' : `изображение ${index + 1}`}
                        </Button>
                      </div>

                    ))}
                  </div>
                )}
              </div>
            )}

            {activeGeneration.status === 'pending' && (
              <div className="text-center py-8">
                <div className="text-sm text-gray-600">
                  {`Генерируем ваше ${activeGeneration.type === 'video' ? 'видео' : 'изображение'}... Это может занять несколько секунд.`}
                </div>
              </div>
            )}

            {activeGeneration.status === 'failed' && (
              <div className="text-center py-4">
                <div className="text-sm text-red-600">
                  {`Генерация не удалась. Ошибка: ${activeGeneration.error}`}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {/* Full-Screen Modal */}
      <Dialog open={isFullScreenOpen} onOpenChange={setIsFullScreenOpen}>
        <VisuallyHidden>
          <DialogTitle className="sr-only">Развернуть на весь экран</DialogTitle>
        </VisuallyHidden>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 bg-black">
          <div className="relative w-full h-full flex items-center justify-center">
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 z-10 text-white hover:bg-white/20 h-8 w-8 p-0"
              onClick={() => setIsFullScreenOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>

            {activeGeneration && activeGeneration.output_files && activeGeneration.output_files.length > 0 && (
              <>
                {activeGeneration.type === 'video' ? (
                  <video
                    src={pb.files.getURL(activeGeneration.expand?.output_files[0], activeGeneration.expand?.output_files[0].file)}
                    controls
                    className="max-w-full max-h-full object-contain"
                    autoPlay
                  >
                    Ваш браузер не поддерживает видео тег.
                  </video>
                ) : (
                  <div className="w-full h-full flex items-center justify-center p-4">
                    <img
                      src={pb.files.getURL(activeGeneration.expand?.output_files[0], activeGeneration.expand?.output_files[0].file)}
                      alt="Созданное изображение"
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
};

export default ActiveGeneration;
