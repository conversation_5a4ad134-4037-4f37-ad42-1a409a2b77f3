import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo3FastConfig: ModelInputConfig = {
  modelId: 'veo3-fast',
  modelName: 'Veo 3 Fast',
  description: 'Более быстрая и дешевая версия Google Veo 3 с возможностями генерации аудио',

  parameters: {
    // Negative prompt
    negative_prompt: {
      ...CommonParameters.negativePrompt,
      description: 'Описание того, чего следует избегать',
      order: 1,
      group: 'advanced'
    },

    // Resolution - specific to veo3-fast
    resolution: {
      type: 'select',
      label: 'Разрешение',
      description: 'Разрешение видео',
      defaultValue: '720p',
      options: [
        { value: '480p', label: '480p (854×480)' },
        { value: '720p', label: '720p (1280×720)' },
        { value: '1080p', label: '1080p (1920×1080)' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.',
      order: 3,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '1080p',
    estimatedTime: '30 seconds - 1 minute'
  }
};
