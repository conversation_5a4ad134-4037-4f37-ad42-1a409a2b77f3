// API service for video and image generation

// Authentication Types
import PocketBase from "pocketbase";

export type RecordIdString = string;

export interface BaseSystemFields {
  id: RecordIdString;
  created: string; // ISO Date String
  updated: string; // ISO Date String
}

// Video Model Types
export type VideoModel = 'hunyuan' | 'seedance-pro' | 'seedance-lite' | 'veo3' | 'veo3-fast' | 'veo2';

export interface CreateVideoRequest {
  model?: string; // "hunyuan", "seedance-pro", "seedance-lite", "veo3", "veo2", defaults to "hunyuan" if not specified
  prompt: string;
  translate_prompt?: boolean;
  enhance_prompt?: boolean;
  negative_prompt?: string; // For Veo-3 model
  image?: string;
  width?: number;
  height?: number;
  duration?: number;
  video_length?: number;
  resolution?: string;
  aspect_ratio?: string;
  infer_steps?: number;
  guidance_scale?: number; // Maps to embedded_guidance_scale for hunyuan on the backend
  fps?: number;
  camera_fixed?: boolean;
  seed?: number;
}

export interface CreateVideoResponse {
  request_id: string;
  prediction_id: string;
  status: string;
}

// Image Model Types
export type ImageModel = 'seedream-3' | 'imagen-4-ultra' | 'flux-kontext-pro' | 'qwen-image' | 'qwen-image-edit';


export interface CreateImageRequest {
  model?: string; // "flux-dev", "seedream-3", "imagen-4-ultra", "flux-kontext-pro", "qwen-image", defaults to "flux-dev" if not specified
  prompt: string;
  translate_prompt?: boolean;
  aspect_ratio?: string;
  image?: string;
  prompt_strength?: number;
  num_outputs?: number;
  num_inference_steps?: number;
  guidance?: number;
  seed?: number;
  output_format?: string;
  output_quality?: number;
  disable_safety_checker?: boolean;
  go_fast?: boolean;
  megapixels?: string;
  // Seedream-3 specific parameters
  size?: string;
  width?: number;
  height?: number;
  guidance_scale?: number;
  // Imagen-4-Ultra specific parameters
  safety_filter_level?: string;
  // FLUX Kontext Pro specific parameters
  input_image?: string;
  safety_tolerance?: number;
  // Qwen-Image specific parameters
  enhance_prompt?: boolean;
  negative_prompt?: string;
  image_size?: string;
  lora_weights?: string;
  lora_scale?: number;
}

export interface CreateImageResponse {
  request_id: string;
  prediction_id: string;
  status: string;
}

// Token Estimation Types
export interface TokenEstimationResponse {
  estimation: number;
}

// Generation Request Status
export type GenerationRequestStatus = 'pending' | 'completed' | 'failed' | 'stalled' | 'canceled';

export type GenerationRequestType = 'image' | 'video';
// Output Result File (from PocketBase output_result_files collection)
export interface OutputResultFile extends BaseSystemFields {
  file: string; // The actual file field
}

export interface GenerationRequestData extends BaseSystemFields{
  user: string;
  model: string;
  error: string;
  replicate_id: string; // This is the prediction_id from Replicate
  status: GenerationRequestStatus;
  type: GenerationRequestType;
  jsondata: any; // Contains type, prompt, input, output, etc.
  output_files?: string[]; // Array of expanded output_result_files
  expand?: {
    output_files: OutputResultFile[];
  };
}

// Legacy types for backward compatibility
export interface GenerationModel {
  id: string;
  name: string;
  description: string;
}

// Model data
const videoModels: GenerationModel[] = [
  { id: 'hunyuan', name: 'Hunyuan Video', description: 'Современная модель генерации видео из текста, способная создавать высококачественные видео с реалистичным движением из текстовых описаний' },
  { id: 'seedance-pro', name: 'Seedance Pro', description: 'Профессиональная версия Seedance, которая предлагает поддержку генерации видео из текста и изображения для видео 5с или 10с, в разрешении 480p и 1080p' },
  { id: 'seedance-lite', name: 'Seedance Lite', description: 'Модель генерации видео, которая предлагает поддержку генерации видео из текста и изображения для видео 5с или 10с, в разрешении 480p и 720p' },
  { id: 'veo3', name: 'Veo 3', description: 'Флагманская модель Google Veo 3 для генерации видео из текста с возможностями генерации аудио' },
  { id: 'veo3-fast', name: 'Veo 3 Fast', description: 'Более быстрая и дешевая версия Google Veo 3 с возможностями генерации аудио' },
  { id: 'veo2', name: 'Veo 2', description: 'Современная модель генерации видео. Veo 2 может точно следовать простым и сложным инструкциям, убедительно имитируя физику реального мира и широкий спектр визуальных стилей.' },
];

const imageModels: GenerationModel[] = [
  // { id: 'flux-dev', name: 'FLUX Dev', description: 'Development version of FLUX model' },
  { id: 'seedream-3', name: 'Seedream 3', description: 'Модель генерации изображений из текста с поддержкой нативной генерации изображений высокого разрешения (2K)' },
  { id: 'imagen-4-ultra', name: 'Imagen 4 Ultra', description: 'Используйте эту ультра-версию Imagen 4, когда качество важнее скорости и стоимости' },
  { id: 'flux-kontext-pro', name: 'FLUX Kontext Pro', description: 'Современная модель с отличным следованием промптам и стабильными результатами' },
  { id: 'qwen-image', name: 'Qwen Image', description: 'Модель генерации изображений с продвинутыми возможностями рендеринга текста и точного редактирования изображений' },
];

// API functions
export const api = {

  // Get models
  getModels: async (type: 'video' | 'image'): Promise<GenerationModel[]> => {

    return type === 'video' ? videoModels : imageModels;
  },

  // Estimation of video generation (now uses PocketBase)
  estimateCreateVideo: async (pb: PocketBase, request: CreateVideoRequest): Promise<TokenEstimationResponse> => {
    try {
      const response = await pb.send('/api/estimate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      return response;
    } catch (error) {
      throw new Error(`Ошибка оценки токенов для видео: ${error}`);
    }
  },

  // Estimation of image generation (now uses PocketBase)
  estimateCreateImage: async (pb: PocketBase, request: CreateImageRequest): Promise<TokenEstimationResponse> => {
    try {
      const response = await pb.send('/api/estimate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      return response;
    } catch (error) {
      throw new Error(`Ошибка оценки токенов для изображения: ${error}`);
    }
  },

  // Video generation (now uses PocketBase)
  createVideo: async (pb: PocketBase, request: CreateVideoRequest): Promise<CreateVideoResponse> => {
    try {
      const correctedRequest = {
        ...request,
        duration: request.duration ? Number(request.duration) : 0,
      }
      return await pb.send('/api/create-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(correctedRequest),
      });
    } catch (error) {
      throw new Error(`Ошибка создания видео: ${error}`);
    }
  },

  // Image generation (now uses PocketBase)
  createImage: async (pb: PocketBase, request: CreateImageRequest): Promise<CreateImageResponse> => {
    try {
      const response = await pb.send('/api/create-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      return response;
    } catch (error) {
      throw new Error(`Ошибка создания изображения: ${error}`);
    }
  },
  createPayment: async (pb: PocketBase, request: {amount: string; referral?: string}): Promise<{url: string}> => {
    try {
      const response = await pb.send('/api/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      return response;
    } catch (error) {
      throw new Error(`Ошибка создания платежа: ${error}`);
    }
  },

  uploadImage: async (pb: PocketBase, file: File): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      const response = await pb.send('/api/upload-image', {
        method: 'POST',
        body: formData
      });
      console.log('file upload response', response);
      if (response && response['urls'] && response['urls']['get']) {
        return response['urls']['get'];
      } else {
        return ''
      }
    } catch (error) {
      throw new Error(`Ошибка загрузки изображения: ${error}`);
    }
  },

  // Get generation request with expanded output_files
  getGenerationRequest: async (pb: any, requestId: string): Promise<GenerationRequestData> => {
    try {
      const response = await pb.collection('generation_requests').getOne(requestId, {
        expand: 'output_files'
      });
      return response;
    } catch (error) {
      throw new Error(`Ошибка получения запроса генерации: ${error}`);
    }
  },

  getUserTokenBalance: async (pb: any, userId: string): Promise<number> => {
    try {
      const response = await pb.collection('users').getOne(userId);
      return response.tokens;
    } catch (error) {
      throw new Error(`Ошибка получения баланса токенов пользователя: ${error}`);
    }
  }
};

// Query keys
export const queryKeys = {
  models: (type: 'video' | 'image') => ['models', type],
  generation: (id: string) => ['generation', id],
  generationRequest: (id: string) => ['generationRequest', id],
  generationHistory: (userId: string, page: number, pageSize: number, filter?: 'all' | 'image' | 'video') => ['generationHistory', userId, page, pageSize, filter] as const,
  userTokenBalance: (userId: string) => ['userTokenBalance', userId]
};

// Mutation keys
export const mutationKeys = {
  createVideo: ['createVideo'],
  createImage: ['createImage'],
  estimateCreateVideo: ['estimateCreateVideo'],
  estimateCreateImage: ['estimateCreateImage'],
  createPayment: ['createPayment'],
  uploadImage: ['uploadImage']
};
