import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';

const features = [
    {
        title: 'Забудьте про дорогие фотосессии и дизайнеров!',
        description: 'С нашим AI-сервисом вы создаёте профессиональные изображения и инфографику для Wildberries и Ozon без студий и специалистов. Просто загрузите описание товара, и нейросеть за минуты выдаст яркие, продающие карточки, экономя ваши деньги и нервы',
        image: 'wildberries/1.webp',
        reverse: false
    },
    {
        title: 'Идеальные карточки без технических заморочек',
        description: 'Больше не нужно ломать голову над размерами, форматами и требованиями маркетплейсов. Наш AI автоматически генерирует изображения, которые на 100% соответствуют стандартам площадок, — просто загружайте и публикуйте!',
        image: 'wildberries/2.webp',
        reverse: true
    },
    {
        title: 'Выделяйтесь среди конкурентов с креативом',
        description: 'Застряли без идей? Наш сервис предлагает уникальные визуальные решения на основе вашего описания. AI создаёт стильные кадры и инфографику, которые цепляют взгляд покупателей и поднимают ваш товар в топ.',
        image: 'wildberries/3.webp',
        reverse: false
    },
    {
        title: 'Экономьте время — получайте результат мгновенно',
        description: 'Создание карточек вручную крадёт часы? С нашим AI вы получаете готовые изображения за пару кликов. Пока другие возятся с фотошопом, вы уже запускаете новые товары и увеличиваете продажи.',
        image: 'wildberries/4.webp',
        reverse: true
    },
    {
        title: 'Мгновенные правки для идеального результата',
        description: 'Хотите изменить стиль или добавить акцент в карточке? Наш AI позволяет быстро вносить корректировки в изображения, чтобы они идеально соответствовали вашему видению, без долгих переделок и ожидания дизайнеров.',
        image: 'wildberries/5.webp',
        reverse: false
    }
]

const LandingWildberries = () => {
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title="Создавайте" titleHighlight="цепляющие карточки для товаров" subtitle="Загружайте товар — получайте крутые карточки! Нейросеть создаст изображения и инфографику для Wildberries по вашему описанию. Без фотосессий и дизайнеров — просто опишите кадр и получите яркий результат для площадки!" secondaryImage='/heroImages/wildberries/landingHero2.jpg' topSecondaryImage='/heroImages/wildberries/landingHero3.jpg' bottomSecondaryImage='/heroImages/wildberries/landingHero4.jpg'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingWildberries;
